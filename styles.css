/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Spiritual Color Palette */
    --primary-purple: #4B0082;
    --gold: #FFD700;
    --white: #FFFFFF;
    --deep-blue: #001F54;
    --vibrant-red: #E53935;
    --ivory: #FAFAF5;
    --light-gold-gradient: linear-gradient(45deg, #fff3b0, #ffd700);

    /* Typography */
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Lato', sans-serif;
    --font-cta: 'Poppins', sans-serif;
    --font-scripture: 'E<PERSON>mond', serif;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    --spacing-3xl: 6rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(75, 0, 130, 0.1);
    --shadow-md: 0 4px 8px rgba(75, 0, 130, 0.15);
    --shadow-lg: 0 8px 16px rgba(75, 0, 130, 0.2);
    --shadow-xl: 0 16px 32px rgba(75, 0, 130, 0.25);
    --shadow-gold: 0 4px 20px rgba(255, 215, 0, 0.3);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-body);
    background-color: var(--ivory);
    color: var(--deep-blue);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--gold);
    z-index: 1000;
    padding: var(--spacing-sm) 0;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.logo h1 {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-purple);
    text-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.nav-links {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-links a {
    color: var(--deep-blue);
    text-decoration: none;
    font-weight: 500;
    font-family: var(--font-cta);
    transition: var(--transition-fast);
    position: relative;
    padding: var(--spacing-xs) var(--spacing-sm);
}

.nav-links a:hover {
    color: var(--primary-purple);
    transform: translateY(-2px);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gold);
    transition: var(--transition-fast);
    transform: translateX(-50%);
}

.nav-links a:hover::after {
    width: 80%;
}

/* Hero Sections */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--deep-blue) 100%);
    color: var(--white);
    margin-top: 70px;
}

.hero.secondary-hero {
    min-height: 80vh;
    background: linear-gradient(135deg, var(--deep-blue) 0%, var(--primary-purple) 100%);
    margin-top: 0;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    z-index: 2;
    position: relative;
}

.secondary-hero .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
}

.hero-text {
    max-width: 600px;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: clamp(2.5rem, 5vw, 4.5rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-md);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-line {
    display: block;
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.8s ease forwards;
}

.hero-line:nth-child(2) {
    animation-delay: 0.2s;
}

.hero-line:nth-child(3) {
    animation-delay: 0.4s;
}

.hero-line.highlight {
    background: var(--light-gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.5));
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-xl);
    opacity: 0;
    animation: fadeInUp 0.8s ease 0.6s forwards;
    font-style: italic;
    font-family: var(--font-scripture);
}

.hero-cta {
    display: flex;
    gap: var(--spacing-md);
    opacity: 0;
    animation: fadeInUp 0.8s ease 0.8s forwards;
    flex-wrap: wrap;
}

/* Buttons */
.cta-primary, .cta-secondary {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1rem;
    font-family: var(--font-cta);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-primary {
    background: var(--vibrant-red);
    color: var(--white);
    box-shadow: var(--shadow-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cta-primary:hover {
    background: #d32f2f;
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.cta-primary:hover::before {
    left: 100%;
}

.cta-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--gold);
}

.cta-secondary:hover {
    background: var(--gold);
    color: var(--primary-purple);
    transform: translateY(-2px);
    box-shadow: var(--shadow-gold);
}

/* Animations */
@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Styles */
.section-title {
    font-family: var(--font-heading);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--primary-purple);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    text-shadow: 0 2px 4px rgba(255, 215, 0, 0.2);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--deep-blue);
    text-align: center;
    margin-bottom: var(--spacing-xl);
    font-style: italic;
    font-family: var(--font-scripture);
}

/* Spiritual Visual Effects */
.spiritual-glow {
    position: absolute;
    top: 50%;
    right: 10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.element {
    position: absolute;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 60px;
    height: 60px;
    top: 20%;
    right: 20%;
    animation-delay: 0s;
}

.element-2 {
    width: 40px;
    height: 40px;
    top: 60%;
    right: 30%;
    animation-delay: 2s;
}

.element-3 {
    width: 80px;
    height: 80px;
    top: 40%;
    right: 10%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Mission Trips Section */
.mission-trips {
    padding: var(--spacing-3xl) 0;
    background: var(--white);
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.mission-text h2 {
    text-align: left;
    margin-bottom: var(--spacing-lg);
}

.mission-description {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
    color: var(--deep-blue);
}

.mission-details {
    font-size: 1rem;
    margin-bottom: var(--spacing-xl);
    color: var(--deep-blue);
    opacity: 0.8;
}

.mission-visual {
    position: relative;
}

.mission-image-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, var(--primary-purple), var(--deep-blue));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.swipe-indicator {
    color: var(--white);
    font-family: var(--font-cta);
    font-weight: 600;
    font-size: 1.2rem;
    letter-spacing: 2px;
}

/* Upcoming Crusades Section */
.upcoming-crusades {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--ivory) 0%, #f5f5f5 100%);
}

.crusades-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.crusade-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.crusade-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.crusade-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.crusade-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.crusade-card:hover .crusade-image img {
    transform: scale(1.05);
}

.crusade-info {
    padding: var(--spacing-lg);
}

.crusade-info h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    color: var(--primary-purple);
    margin-bottom: var(--spacing-md);
}

.crusade-description {
    color: var(--deep-blue);
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.crusade-details {
    margin-bottom: var(--spacing-lg);
}

.detail-item {
    display: flex;
    margin-bottom: var(--spacing-sm);
    align-items: flex-start;
}

.detail-item strong {
    color: var(--primary-purple);
    font-family: var(--font-cta);
    min-width: 80px;
    margin-right: var(--spacing-sm);
}

.detail-item span {
    color: var(--deep-blue);
}

.no-crusades-message {
    text-align: center;
    padding: var(--spacing-3xl);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.no-crusades-message h3 {
    font-family: var(--font-heading);
    color: var(--primary-purple);
    margin-bottom: var(--spacing-md);
}

.view-highlights-cta {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Give Section */
.give-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--deep-blue) 100%);
    color: var(--white);
}

.give-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.give-header .section-title {
    color: var(--white);
    text-align: left;
}

.give-subtitle {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    color: var(--gold);
    margin-bottom: var(--spacing-md);
}

.join-us {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.give-description {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
}

.impact-list {
    margin-bottom: var(--spacing-xl);
}

.impact-item {
    padding: var(--spacing-sm) 0;
    border-left: 3px solid var(--gold);
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.donation-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.donation-form h4 {
    font-family: var(--font-heading);
    color: var(--gold);
    margin-bottom: var(--spacing-md);
}

.payment-method-selector {
    margin-bottom: var(--spacing-lg);
}

.payment-options {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.payment-option {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: var(--white);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.payment-option.active,
.payment-option:hover {
    border-color: var(--gold);
    background: rgba(255, 215, 0, 0.1);
}

.espees-icon {
    width: 20px;
    height: 20px;
}

.amount-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.amount-btn {
    padding: var(--spacing-md);
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: var(--white);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: var(--font-cta);
    font-weight: 600;
}

.amount-btn:hover,
.amount-btn.active {
    border-color: var(--gold);
    background: rgba(255, 215, 0, 0.1);
}

.custom-amount {
    grid-column: 1 / -1;
    margin-top: var(--spacing-sm);
}

.custom-amount label {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--gold);
    font-weight: 500;
}

.amount-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: var(--radius-md);
    font-size: 1rem;
}

.amount-input:focus {
    outline: none;
    border-color: var(--gold);
}

.donor-info {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.form-input {
    padding: var(--spacing-md);
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: var(--radius-md);
    font-size: 1rem;
}

.form-input:focus {
    outline: none;
    border-color: var(--gold);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.donate-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--vibrant-red);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-cta);
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition-normal);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-lg);
}

.donate-btn:hover {
    background: #d32f2f;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.espees-code {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.espees-code h4 {
    margin-bottom: var(--spacing-sm);
}

.code {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--gold);
    background: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
}

.security-notice {
    text-align: center;
    opacity: 0.8;
}

/* Highlights Section */
.highlights-section {
    padding: var(--spacing-3xl) 0;
    background: var(--white);
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.highlight-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    position: relative;
}

.highlight-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.highlight-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    position: relative;
}

.highlight-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.highlight-card:hover .highlight-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    background: var(--vibrant-red);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    font-family: var(--font-cta);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.highlight-info {
    padding: var(--spacing-lg);
}

.highlight-info h3 {
    font-family: var(--font-heading);
    font-size: 1.4rem;
    color: var(--primary-purple);
    margin-bottom: var(--spacing-md);
}

.highlight-description {
    color: var(--deep-blue);
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.highlight-details {
    margin-bottom: var(--spacing-lg);
}

.detail-row {
    display: flex;
    margin-bottom: var(--spacing-xs);
}

.detail-label {
    font-weight: 600;
    color: var(--primary-purple);
    min-width: 100px;
    margin-right: var(--spacing-sm);
}

.detail-value {
    color: var(--deep-blue);
}

/* Media Section */
.media-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--ivory) 0%, #f5f5f5 100%);
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.media-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.media-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.media-thumbnail {
    width: 100%;
    height: 250px;
    overflow: hidden;
    position: relative;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(229, 57, 53, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    transition: var(--transition-normal);
}

.play-button:hover {
    background: var(--vibrant-red);
    transform: translate(-50%, -50%) scale(1.1);
}

.media-info {
    padding: var(--spacing-lg);
}

.media-info h3 {
    font-family: var(--font-heading);
    font-size: 1.4rem;
    color: var(--primary-purple);
    margin-bottom: var(--spacing-md);
}

.media-cta {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* Products Section */
.products-section {
    padding: var(--spacing-3xl) 0;
    background: var(--white);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.product-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--gold);
}

.product-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-info {
    padding: var(--spacing-lg);
}

.product-info h3 {
    font-family: var(--font-heading);
    font-size: 1.4rem;
    color: var(--primary-purple);
    margin-bottom: var(--spacing-md);
}

.product-description {
    color: var(--deep-blue);
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

/* App Download Section */
.app-download-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--deep-blue) 0%, var(--primary-purple) 100%);
    color: var(--white);
}

.app-download-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.app-info {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.app-icon img {
    width: 100px;
    height: 100px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.app-text h2 {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    color: var(--gold);
}

.download-label {
    background: var(--vibrant-red);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-family: var(--font-cta);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-top: var(--spacing-md);
}

.download-options h3 {
    font-family: var(--font-heading);
    color: var(--gold);
    margin-bottom: var(--spacing-lg);
}

.download-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.download-link {
    display: block;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--white);
    transition: var(--transition-normal);
}

.download-link:hover {
    background: rgba(255, 215, 0, 0.1);
    border-color: var(--gold);
    transform: translateY(-2px);
}

.download-link strong {
    display: block;
    font-family: var(--font-cta);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.download-link span {
    font-size: 0.9rem;
    opacity: 0.8;
}

.download-note {
    text-align: center;
    opacity: 0.8;
    font-style: italic;
}

/* Registration Section */
.registration-section {
    padding: var(--spacing-3xl) 0;
    background: var(--white);
}

.registration-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.registration-card {
    background: var(--white);
    border: 3px solid var(--gold);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.registration-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    transition: var(--transition-slow);
}

.registration-card:hover::before {
    left: 100%;
}

.registration-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-purple);
}

.registration-card h3 {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    color: var(--primary-purple);
    margin-bottom: var(--spacing-md);
}

.registration-card p {
    color: var(--deep-blue);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .hero-cta {
        justify-content: center;
    }

    .mission-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .mission-text h2 {
        text-align: center;
    }

    .crusades-grid {
        grid-template-columns: 1fr;
    }

    .give-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .give-header .section-title {
        text-align: center;
    }

    .highlights-grid {
        grid-template-columns: 1fr;
    }

    .media-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .app-download-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .app-info {
        flex-direction: column;
        text-align: center;
    }

    .registration-options {
        grid-template-columns: 1fr;
    }

    .amount-buttons {
        grid-template-columns: 1fr;
    }

    .payment-options {
        flex-direction: column;
    }

    .media-cta {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero {
        margin-top: 60px;
    }

    .navbar {
        padding: var(--spacing-xs) 0;
    }

    .logo h1 {
        font-size: 1.4rem;
    }

    .hero-title {
        font-size: clamp(1.8rem, 6vw, 2.5rem);
    }

    .section-title {
        font-size: clamp(1.5rem, 5vw, 2rem);
    }

    .crusade-card,
    .highlight-card,
    .product-card {
        margin: 0 var(--spacing-sm);
    }

    .donation-form {
        padding: var(--spacing-lg);
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-gold {
    color: var(--gold);
}

.text-purple {
    color: var(--primary-purple);
}

.bg-white {
    background-color: var(--white);
}

.bg-purple {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--deep-blue) 100%);
}

.mb-lg {
    margin-bottom: var(--spacing-lg);
}

.mt-lg {
    margin-top: var(--spacing-lg);
}

.p-lg {
    padding: var(--spacing-lg);
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Print Styles */
@media print {
    .navbar,
    .hero-visual,
    .floating-elements,
    .spiritual-glow {
        display: none;
    }

    .hero {
        margin-top: 0;
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }

    body {
        background: white;
        color: black;
    }
}